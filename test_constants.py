#!/usr/bin/env python3
"""
Test script to verify constants and enum conversion methods work correctly
"""

import sys
import os

# Add the application directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'application'))

from app.core.constants import OrderStatus, PaymentStatus, OrderStatusEnum, PaymentStatusEnum

def test_order_status_conversions():
    """Test OrderStatus conversion methods"""
    print("🧪 Testing OrderStatus conversions...")
    
    # Test integer to enum string conversion
    print("\n📝 Testing integer to enum string conversion:")
    test_cases = [
        (0, "draft"),
        (10, "open"),
        (11, "fulfilled"),
        (12, "partially_fulfilled"),
        (13, "unfulfilled"),
        (14, "canceled"),
        (21, "wms_synced"),
        (31, "tms_synced"),
    ]
    
    for code, expected_enum in test_cases:
        result = OrderStatus.to_enum_string(code)
        if result == expected_enum:
            print(f"✅ {code} -> '{result}'")
        else:
            print(f"❌ {code} -> '{result}' (expected '{expected_enum}')")
    
    # Test enum string to integer conversion
    print("\n📝 Testing enum string to integer conversion:")
    for expected_code, enum_str in test_cases:
        result = OrderStatus.from_enum_string(enum_str)
        if result == expected_code:
            print(f"✅ '{enum_str}' -> {result}")
        else:
            print(f"❌ '{enum_str}' -> {result} (expected {expected_code})")
    
    # Test normalize_status method
    print("\n📝 Testing normalize_status method:")
    normalize_test_cases = [
        (10, 10),  # Integer input
        ("open", 10),  # Enum string input
        (OrderStatusEnum.OPEN, 10),  # Enum input
        ("invalid", None),  # Invalid input
        (999, None),  # Invalid integer
    ]
    
    for input_val, expected in normalize_test_cases:
        result = OrderStatus.normalize_status(input_val)
        if result == expected:
            print(f"✅ normalize_status({input_val}) -> {result}")
        else:
            print(f"❌ normalize_status({input_val}) -> {result} (expected {expected})")

def test_payment_status_conversions():
    """Test PaymentStatus conversion methods"""
    print("\n🧪 Testing PaymentStatus conversions...")
    
    # Test integer to enum string conversion
    print("\n📝 Testing integer to enum string conversion:")
    test_cases = [
        (50, "pending"),
        (51, "completed"),
        (52, "failed"),
        (53, "refunded"),
    ]
    
    for code, expected_enum in test_cases:
        result = PaymentStatus.to_enum_string(code)
        if result == expected_enum:
            print(f"✅ {code} -> '{result}'")
        else:
            print(f"❌ {code} -> '{result}' (expected '{expected_enum}')")
    
    # Test enum string to integer conversion
    print("\n📝 Testing enum string to integer conversion:")
    for expected_code, enum_str in test_cases:
        result = PaymentStatus.from_enum_string(enum_str)
        if result == expected_code:
            print(f"✅ '{enum_str}' -> {result}")
        else:
            print(f"❌ '{enum_str}' -> {result} (expected {expected_code})")
    
    # Test normalize_status method
    print("\n📝 Testing normalize_status method:")
    normalize_test_cases = [
        (50, 50),  # Integer input
        ("pending", 50),  # Enum string input
        (PaymentStatusEnum.PENDING, 50),  # Enum input
        ("invalid", None),  # Invalid input
        (999, None),  # Invalid integer
    ]
    
    for input_val, expected in normalize_test_cases:
        result = PaymentStatus.normalize_status(input_val)
        if result == expected:
            print(f"✅ normalize_status({input_val}) -> {result}")
        else:
            print(f"❌ normalize_status({input_val}) -> {result} (expected {expected})")

def test_enum_classes():
    """Test Python enum classes"""
    print("\n🧪 Testing Python enum classes...")
    
    # Test OrderStatusEnum
    print("\n📝 Testing OrderStatusEnum:")
    print(f"✅ OrderStatusEnum.OPEN.value = '{OrderStatusEnum.OPEN.value}'")
    print(f"✅ OrderStatusEnum.FULFILLED.value = '{OrderStatusEnum.FULFILLED.value}'")
    print(f"✅ OrderStatusEnum.CANCELED.value = '{OrderStatusEnum.CANCELED.value}'")
    
    # Test PaymentStatusEnum
    print("\n📝 Testing PaymentStatusEnum:")
    print(f"✅ PaymentStatusEnum.PENDING.value = '{PaymentStatusEnum.PENDING.value}'")
    print(f"✅ PaymentStatusEnum.COMPLETED.value = '{PaymentStatusEnum.COMPLETED.value}'")
    print(f"✅ PaymentStatusEnum.FAILED.value = '{PaymentStatusEnum.FAILED.value}'")

def test_backward_compatibility():
    """Test backward compatibility with legacy string mappings"""
    print("\n🧪 Testing backward compatibility...")
    
    # Test legacy string mappings still work
    print("\n📝 Testing legacy string mappings:")
    legacy_test_cases = [
        ("oms_open", 10),
        ("oms_fulfilled", 11),
        ("oms_canceled", 14),
        ("wms_synced", 21),
    ]
    
    for legacy_str, expected_code in legacy_test_cases:
        result = OrderStatus.from_legacy_string(legacy_str)
        if result == expected_code:
            print(f"✅ from_legacy_string('{legacy_str}') -> {result}")
        else:
            print(f"❌ from_legacy_string('{legacy_str}') -> {result} (expected {expected_code})")
    
    # Test normalize_status handles legacy strings
    print("\n📝 Testing normalize_status with legacy strings:")
    for legacy_str, expected_code in legacy_test_cases:
        result = OrderStatus.normalize_status(legacy_str)
        if result == expected_code:
            print(f"✅ normalize_status('{legacy_str}') -> {result}")
        else:
            print(f"❌ normalize_status('{legacy_str}') -> {result} (expected {expected_code})")

def main():
    """Run all tests"""
    print("🧪 Starting constants and enum conversion tests...\n")
    
    try:
        test_order_status_conversions()
        test_payment_status_conversions()
        test_enum_classes()
        test_backward_compatibility()
        
        print("\n🎉 All constants tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
