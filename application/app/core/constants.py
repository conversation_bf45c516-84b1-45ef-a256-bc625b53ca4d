"""
Core constants for the Rozana OMS application

This module contains all the core constants used across the application,
including order status codes, system-wide enums, and other shared values.
"""

from enum import Enum

class OrderStatus:
    """Order status constants for lifecycle management"""
    
    # Rozana (OMS) statuses
    DRAFT = 0
    OPEN = 10
    FULFILLED = 11
    PARTIALLY_FULFILLED = 12
    UNFULFILLED = 13
    CANCELED = 14
    RETURN = 15
    
    # WMS statuses
    WMS_SYNCED = 21
    WMS_SYNC_FAILED = 22

    # WMS processing statuses - numerical codes
    WMS_OPEN = 23
    WMS_INPROGRESS = 24
    WMS_PICKED = 25
    WMS_FULFILLED = 26
    WMS_INVOICED = 27

    
    # TMS statuses
    TMS_SYNCED = 31
    TMS_SYNC_FAILED = 32

    # String representation of statuses used in database (legacy)
    DB_STATUS_MAP = {
        # OMS statuses
        "oms_draft": DRAFT,
        "oms_open": OPEN,
        "oms_fulfilled": FULFILLED,
        "oms_partial_fulfilled": PARTIALLY_FULFILLED,
        "oms_unfulfilled": UNFULF<PERSON>LED,
        "oms_canceled": <PERSON><PERSON><PERSON><PERSON>,

         # WMS statuses - string to code mapping
        "wms_synced": WMS_SYNCED,
        "wms_sync_failed": WMS_SYNC_FAILED,
        "open" : WMS_OPEN,
        "in_progress": WMS_INPROGRESS,
        "picked": WMS_PICKED,
        "fulfilled": WMS_FULFILLED,
        "invoiced": WMS_INVOICED,

        # TMS statuses
        "tms_synced": TMS_SYNCED,
        "tms_sync_failed": TMS_SYNC_FAILED,
    }

    # Reverse mapping: code to string for database operations (legacy)
    CODE_TO_DB_STATUS = {v: k for k, v in DB_STATUS_MAP.items()}

    # New enum string mappings for database enum types
    ENUM_STATUS_MAP = {
        # OMS statuses
        "draft": DRAFT,
        "open": OPEN,
        "fulfilled": FULFILLED,
        "partially_fulfilled": PARTIALLY_FULFILLED,
        "unfulfilled": UNFULFILLED,
        "canceled": CANCELED,
        "return": RETURN,

        # WMS statuses
        "wms_synced": WMS_SYNCED,
        "wms_sync_failed": WMS_SYNC_FAILED,
        "wms_open": WMS_OPEN,
        "wms_inprogress": WMS_INPROGRESS,
        "wms_picked": WMS_PICKED,
        "wms_fulfilled": WMS_FULFILLED,
        "wms_invoiced": WMS_INVOICED,

        # TMS statuses
        "tms_synced": TMS_SYNCED,
        "tms_sync_failed": TMS_SYNC_FAILED,
    }

    # Reverse mapping: integer code to enum string
    CODE_TO_ENUM_STATUS = {v: k for k, v in ENUM_STATUS_MAP.items()}
    
    @classmethod
    def get_status_name(cls, status_code: int) -> str:
        """Get human-readable status name from status code"""
        status_map = {
            cls.OPEN: "Open",
            cls.FULFILLED: "Fulfilled",
            cls.PARTIALLY_FULFILLED: "Partially Fulfilled",
            cls.UNFULFILLED: "UnFulfilled",
            cls.CANCELED: "Canceled",
            cls.WMS_SYNCED: "WMS Synced",
            cls.WMS_SYNC_FAILED: "WMS Sync Failed",
            cls.TMS_SYNCED: "TMS Synced",
            cls.TMS_SYNC_FAILED: "TMS Sync Failed",
            cls.DRAFT: "Draft"
        }
        return status_map.get(status_code, f"Unknown Status ({status_code})")
    
    @classmethod
    def is_rozana_status(cls, status_code: int) -> bool:
        """Check if status code is a Rozana (OMS) status"""
        return status_code in [cls.OPEN, cls.FULFILLED, cls.PARTIALLY_FULFILLED, cls.UNFULFILLED, cls.CANCELED]
    
    @classmethod
    def is_wms_status(cls, status_code: int) -> bool:
        """Check if status code is a WMS status"""
        return status_code in [
            cls.WMS_SYNCED, cls.WMS_SYNC_FAILED, cls.WMS_OPEN, cls.WMS_PICKED, cls.WMS_INPROGRESS, cls.WMS_FULFILLED, cls.WMS_INVOICED
        ]

    @classmethod
    def is_tms_status(cls, status_code: int) -> bool:
        """Check if status code is a TMS status"""
        return status_code in [cls.TMS_SYNCED, cls.TMS_SYNC_FAILED]

    @classmethod
    def from_enum_string(cls, enum_status: str) -> int:
        """Convert enum string status to integer constant"""
        return cls.ENUM_STATUS_MAP.get(enum_status)

    @classmethod
    def to_enum_string(cls, status_code: int) -> str:
        """Convert integer constant to enum string status"""
        return cls.CODE_TO_ENUM_STATUS.get(status_code)

    @classmethod
    def from_legacy_string(cls, legacy_status: str) -> int:
        """Convert legacy string status to integer constant"""
        return cls.DB_STATUS_MAP.get(legacy_status)

    @classmethod
    def to_legacy_string(cls, status_code: int) -> str:
        """Convert integer constant to legacy string status"""
        return cls.CODE_TO_DB_STATUS.get(status_code)

    @classmethod
    def normalize_status(cls, status) -> int:
        """
        Normalize status input to integer constant.
        Accepts: integer, enum string, legacy string, or OrderStatusEnum
        Returns: integer constant or None if invalid
        """
        # Handle OrderStatusEnum objects
        if hasattr(status, 'value') and hasattr(status, 'name'):
            # It's likely an enum object, get its string value
            enum_str = status.value if hasattr(status, 'value') else str(status)
            return cls.from_enum_string(enum_str)
        elif isinstance(status, int):
            # Validate that the integer is a known status code
            if cls.to_enum_string(status) is not None:
                return status
            else:
                return None
        elif isinstance(status, str):
            # Try enum string first
            enum_value = cls.from_enum_string(status)
            if enum_value is not None:
                return enum_value
            # Fall back to legacy string
            return cls.from_legacy_string(status)
        return None

    @classmethod
    def is_valid_status(cls, status) -> bool:
        """Check if status is valid (integer, enum string, or legacy string)"""
        normalized = cls.normalize_status(status)
        return normalized is not None
    



class PaymentStatus:
    """Payment status constants for payment lifecycle management"""
    
    # Payment statuses (integer-based for consistency with OrderStatus)
    PENDING = 50
    COMPLETED = 51
    FAILED = 52
    REFUNDED = 53
    
    # Legacy integer-based mapping (for backward compatibility)
    DB_STATUS_MAP = {
        50 : PENDING,
        51 : COMPLETED,
        52 : FAILED,
        53 : REFUNDED,
    }

    # Legacy reverse mapping for database operations
    STATUS_TO_DB_MAP = {
        50: "pending",
        51: "completed",
        52: "failed",
        53: "refunded",
    }

    # New enum string mappings for database enum types
    ENUM_STATUS_MAP = {
        "pending": PENDING,
        "completed": COMPLETED,
        "failed": FAILED,
        "refunded": REFUNDED,
    }

    # Reverse mapping: integer code to enum string
    CODE_TO_ENUM_STATUS = {v: k for k, v in ENUM_STATUS_MAP.items()}
    
    # Status descriptions
    STATUS_DESCRIPTIONS = {
        50: "Payment Pending",
        51: "Payment Completed", 
        52: "Payment Failed",
        53: "Payment Refunded"
    }
    
    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description for payment status"""
        return cls.STATUS_DESCRIPTIONS.get(status_code, f"Unknown Payment Status ({status_code})")
    
    @classmethod
    def is_valid_status(cls, status_code: int) -> bool:
        """Check if status code is a valid payment status"""
        return status_code in [cls.PENDING, cls.COMPLETED, cls.FAILED, cls.REFUNDED]
    
    @classmethod
    def from_db_string(cls, db_status: str) -> int:
        """Convert database string status to integer constant"""
        return cls.DB_STATUS_MAP.get(db_status, cls.PENDING)
    
    @classmethod
    def to_db_string(cls, status_code: int) -> str:
        """Convert integer constant to database string status"""
        return cls.STATUS_TO_DB_MAP.get(status_code, "pending")
    
    @classmethod
    def is_final_status(cls, status_code: int) -> bool:
        """Check if payment status is final (completed, failed, or refunded)"""
        return status_code in [cls.COMPLETED, cls.FAILED, cls.REFUNDED]

    @classmethod
    def from_enum_string(cls, enum_status: str) -> int:
        """Convert enum string status to integer constant"""
        return cls.ENUM_STATUS_MAP.get(enum_status)

    @classmethod
    def to_enum_string(cls, status_code: int) -> str:
        """Convert integer constant to enum string status"""
        return cls.CODE_TO_ENUM_STATUS.get(status_code)

    @classmethod
    def normalize_status(cls, status) -> int:
        """
        Normalize status input to integer constant.
        Accepts: integer, enum string, or PaymentStatusEnum
        Returns: integer constant or None if invalid
        """
        # Handle PaymentStatusEnum objects
        if hasattr(status, 'value') and hasattr(status, 'name'):
            # It's likely an enum object, get its string value
            enum_str = status.value if hasattr(status, 'value') else str(status)
            return cls.from_enum_string(enum_str)
        elif isinstance(status, int):
            # Validate that the integer is a known status code
            if cls.to_enum_string(status) is not None:
                return status
            else:
                return None
        elif isinstance(status, str):
            return cls.from_enum_string(status)
        return None

    @classmethod
    def is_valid_payment_status(cls, status) -> bool:
        """Check if status is valid (integer or enum string)"""
        normalized = cls.normalize_status(status)
        return normalized is not None and cls.is_valid_status(normalized)


# Python Enum classes for type safety and better IDE support
class OrderStatusEnum(Enum):
    """Python enum for order status values"""
    DRAFT = "draft"
    OPEN = "open"
    FULFILLED = "fulfilled"
    PARTIALLY_FULFILLED = "partially_fulfilled"
    UNFULFILLED = "unfulfilled"
    CANCELED = "canceled"
    RETURN = "return"
    WMS_SYNCED = "wms_synced"
    WMS_SYNC_FAILED = "wms_sync_failed"
    WMS_OPEN = "wms_open"
    WMS_INPROGRESS = "wms_inprogress"
    WMS_PICKED = "wms_picked"
    WMS_FULFILLED = "wms_fulfilled"
    WMS_INVOICED = "wms_invoiced"
    TMS_SYNCED = "tms_synced"
    TMS_SYNC_FAILED = "tms_sync_failed"


class PaymentStatusEnum(Enum):
    """Python enum for payment status values"""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"


class SystemConstants:
    """System-wide constants"""

    # Default ETA hours for new orders
    DEFAULT_ETA_HOURS = 24

    # Order ID prefix length
    ORDER_ID_PREFIX_LENGTH = 4

    # Maximum retry attempts for external service calls
    MAX_RETRY_ATTEMPTS = 3

    # Default timeout for external API calls (seconds)
    DEFAULT_API_TIMEOUT = 30


class APIConstants:
    """API-related constants"""
    
    # API version
    API_VERSION = "v1"
    
    # Default page size for paginated responses
    DEFAULT_PAGE_SIZE = 20
    
    # Maximum page size for paginated responses
    MAX_PAGE_SIZE = 100
