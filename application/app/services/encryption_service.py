from Crypto import Random
from Crypto.Cipher import AES
import base64
import binascii
import os
import logging

logger = logging.getLogger(__name__)

KEY_SIZE = 16
ENCRYPTION_KEY = b"thisisasamplepassphraseforencoding"

class EncryptionService:
    """Service for AES encryption using CBC mode with PKCS5 padding."""
    
    @staticmethod
    def pad_pkcs5(text: str) -> str:
        """Apply PKCS5 padding to text."""
        try:
            length = KEY_SIZE - (len(text) % KEY_SIZE)
            return text + chr(length) * length
        except Exception as e:
            logger.error(f"Padding exception in pad_pkcs5(): {e}")
            raise Exception("Failed to apply PKCS5 padding")

    @staticmethod
    def encrypt(plaintext: str, passphrase: bytes, iv: bytes) -> str:
        """Encrypt plaintext using AES CBC (128-bits)."""
        try:
            aes = AES.new(passphrase, AES.MODE_CBC, iv)
            padded_text = EncryptionService.pad_pkcs5(plaintext)
            encrypted = aes.encrypt(padded_text.encode('utf-8'))
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"Encryption exception in encrypt(): {e}")
            raise Exception("Failed to encrypt plaintext")

    @staticmethod
    def encrypt_customer_code(customer_code: str) -> tuple[str, str]:
        """
        Encrypt customer code and return encrypted text with IV.
        
        Returns:
            tuple: (encrypted_text, iv_hex)
        """
        try:
            # Ensure that the key is no more than 16-bytes long
            key = ENCRYPTION_KEY[0:KEY_SIZE] if len(ENCRYPTION_KEY) > KEY_SIZE else ENCRYPTION_KEY
            
            # Generate initialization vector
            randm = os.urandom(8)
            iv = binascii.hexlify(randm)
            
            # Encrypt customer code
            encrypted_text = EncryptionService.encrypt(customer_code, key, iv)
            
            logger.info(f"Successfully encrypted customer code")
            return encrypted_text, iv.decode('utf-8')
            
        except Exception as e:
            logger.error(f"An exception occurred while encrypting customer code: {e}")
            raise Exception("Failed to encrypt customer code")
