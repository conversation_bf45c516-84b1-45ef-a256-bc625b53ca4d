"""add_enum_status_columns

Revision ID: 705b171b5bd2
Revises: 048f2c9b56b1
Create Date: 2025-08-12 10:21:11.902356

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '705b171b5bd2'
down_revision: Union[str, Sequence[str], None] = '048f2c9b56b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add enum status columns alongside existing integer columns."""

    # Add enum status columns to orders table
    op.add_column('orders', sa.Column('status_enum', sa.Enum(
        'draft', 'open', 'fulfilled', 'partially_fulfilled', 'unfulfilled',
        'canceled', 'return', 'wms_synced', 'wms_sync_failed', 'wms_open',
        'wms_inprogress', 'wms_picked', 'wms_fulfilled', 'wms_invoiced',
        'tms_synced', 'tms_sync_failed', name='order_status_enum'
    ), nullable=True))

    # Add enum status columns to order_items table
    op.add_column('order_items', sa.Column('status_enum', sa.Enum(
        'draft', 'open', 'fulfilled', 'partially_fulfilled', 'unfulfilled',
        'canceled', 'return', 'wms_synced', 'wms_sync_failed', 'wms_open',
        'wms_inprogress', 'wms_picked', 'wms_fulfilled', 'wms_invoiced',
        'tms_synced', 'tms_sync_failed', name='order_status_enum'
    ), nullable=True))

    # Add enum status columns to payment_details table
    op.add_column('payment_details', sa.Column('payment_status_enum', sa.Enum(
        'pending', 'completed', 'failed', 'refunded', name='payment_status_enum'
    ), nullable=True))

    # Populate enum columns with values converted from integer columns
    # This ensures data consistency during migration

    # Update orders.status_enum based on orders.status
    op.execute("""
        UPDATE orders SET status_enum = CASE
            WHEN status = 0 THEN 'draft'::order_status_enum
            WHEN status = 10 THEN 'open'::order_status_enum
            WHEN status = 11 THEN 'fulfilled'::order_status_enum
            WHEN status = 12 THEN 'partially_fulfilled'::order_status_enum
            WHEN status = 13 THEN 'unfulfilled'::order_status_enum
            WHEN status = 14 THEN 'canceled'::order_status_enum
            WHEN status = 15 THEN 'return'::order_status_enum
            WHEN status = 21 THEN 'wms_synced'::order_status_enum
            WHEN status = 22 THEN 'wms_sync_failed'::order_status_enum
            WHEN status = 23 THEN 'wms_open'::order_status_enum
            WHEN status = 24 THEN 'wms_inprogress'::order_status_enum
            WHEN status = 25 THEN 'wms_picked'::order_status_enum
            WHEN status = 26 THEN 'wms_fulfilled'::order_status_enum
            WHEN status = 27 THEN 'wms_invoiced'::order_status_enum
            WHEN status = 31 THEN 'tms_synced'::order_status_enum
            WHEN status = 32 THEN 'tms_sync_failed'::order_status_enum
            ELSE 'open'::order_status_enum
        END
    """)

    # Update order_items.status_enum based on order_items.status
    op.execute("""
        UPDATE order_items SET status_enum = CASE
            WHEN status = 0 THEN 'draft'::order_status_enum
            WHEN status = 10 THEN 'open'::order_status_enum
            WHEN status = 11 THEN 'fulfilled'::order_status_enum
            WHEN status = 12 THEN 'partially_fulfilled'::order_status_enum
            WHEN status = 13 THEN 'unfulfilled'::order_status_enum
            WHEN status = 14 THEN 'canceled'::order_status_enum
            WHEN status = 15 THEN 'return'::order_status_enum
            WHEN status = 21 THEN 'wms_synced'::order_status_enum
            WHEN status = 22 THEN 'wms_sync_failed'::order_status_enum
            WHEN status = 23 THEN 'wms_open'::order_status_enum
            WHEN status = 24 THEN 'wms_inprogress'::order_status_enum
            WHEN status = 25 THEN 'wms_picked'::order_status_enum
            WHEN status = 26 THEN 'wms_fulfilled'::order_status_enum
            WHEN status = 27 THEN 'wms_invoiced'::order_status_enum
            WHEN status = 31 THEN 'tms_synced'::order_status_enum
            WHEN status = 32 THEN 'tms_sync_failed'::order_status_enum
            ELSE 'open'::order_status_enum
        END
    """)

    # Update payment_details.payment_status_enum based on payment_details.payment_status
    op.execute("""
        UPDATE payment_details SET payment_status_enum = CASE
            WHEN payment_status = 50 THEN 'pending'::payment_status_enum
            WHEN payment_status = 51 THEN 'completed'::payment_status_enum
            WHEN payment_status = 52 THEN 'failed'::payment_status_enum
            WHEN payment_status = 53 THEN 'refunded'::payment_status_enum
            ELSE 'pending'::payment_status_enum
        END
    """)


def downgrade() -> None:
    """Remove enum status columns."""

    # Remove enum columns
    op.drop_column('orders', 'status_enum')
    op.drop_column('order_items', 'status_enum')
    op.drop_column('payment_details', 'payment_status_enum')
