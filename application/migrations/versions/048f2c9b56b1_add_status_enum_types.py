"""add_status_enum_types

Revision ID: 048f2c9b56b1
Revises: ae_add_typesense_fields
Create Date: 2025-08-12 10:16:42.226754

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '048f2c9b56b1'
down_revision: Union[str, Sequence[str], None] = 'ae_add_typesense_fields'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add enum types for order status and payment status."""

    # Create order_status_enum type
    order_status_enum = sa.Enum(
        'draft',           # 0
        'open',            # 10
        'fulfilled',       # 11
        'partially_fulfilled',  # 12
        'unfulfilled',     # 13
        'canceled',        # 14
        'return',          # 15
        'wms_synced',      # 21
        'wms_sync_failed', # 22
        'wms_open',        # 23
        'wms_inprogress',  # 24
        'wms_picked',      # 25
        'wms_fulfilled',   # 26
        'wms_invoiced',    # 27
        'tms_synced',      # 31
        'tms_sync_failed', # 32
        name='order_status_enum'
    )
    order_status_enum.create(op.get_bind())

    # Create payment_status_enum type
    payment_status_enum = sa.Enum(
        'pending',    # 50
        'completed',  # 51
        'failed',     # 52
        'refunded',   # 53
        name='payment_status_enum'
    )
    payment_status_enum.create(op.get_bind())


def downgrade() -> None:
    """Remove enum types for order status and payment status."""

    # Drop the enum types
    op.execute("DROP TYPE IF EXISTS order_status_enum")
    op.execute("DROP TYPE IF EXISTS payment_status_enum")
