#!/usr/bin/env python3
"""
Test script to verify enum functionality works correctly
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        print("✅ Health check passed")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def test_create_order_with_enum_status():
    """Test creating an order with enum status"""
    print("\nTesting order creation with enum status...")
    
    order_data = {
        "customer_id": "test_customer_001",
        "customer_name": "Test Customer",
        "facility_id": "facility_001",
        "facility_name": "Test Facility",
        "status": "open",  # Using enum string
        "total_amount": 100.50,
        "is_approved": True,
        "items": [
            {
                "sku": "TEST_SKU_001",
                "quantity": 2,
                "unit_price": 25.00,
                "sale_price": 50.00
            }
        ],
        "address": {
            "full_name": "Test Customer",
            "phone_number": "**********",
            "address_line1": "123 Test Street",
            "city": "Test City",
            "state": "Test State",
            "postal_code": "12345",
            "country": "india",
            "type_of_address": "home"
        },
        "payment": {
            "payment_mode": "cod",
            "create_payment_order": False
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/app/v1/create_order", json=order_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Order created successfully: {result.get('order_id')}")
            return result.get('order_id')
        else:
            print(f"❌ Order creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Order creation error: {e}")
        return None

def test_create_order_with_integer_status():
    """Test creating an order with integer status"""
    print("\nTesting order creation with integer status...")
    
    order_data = {
        "customer_id": "test_customer_002",
        "customer_name": "Test Customer 2",
        "facility_id": "facility_001",
        "facility_name": "Test Facility",
        "status": 10,  # Using integer code
        "total_amount": 75.25,
        "is_approved": True,
        "items": [
            {
                "sku": "TEST_SKU_002",
                "quantity": 1,
                "unit_price": 75.25,
                "sale_price": 75.25
            }
        ],
        "address": {
            "full_name": "Test Customer 2",
            "phone_number": "1234567891",
            "address_line1": "456 Test Avenue",
            "city": "Test City",
            "state": "Test State",
            "postal_code": "12346",
            "country": "india",
            "type_of_address": "work"
        },
        "payment": {
            "payment_mode": "cod",
            "create_payment_order": False
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/app/v1/create_order", json=order_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Order created successfully: {result.get('order_id')}")
            return result.get('order_id')
        else:
            print(f"❌ Order creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Order creation error: {e}")
        return None

def test_update_order_status_with_enum(order_id):
    """Test updating order status with enum string"""
    if not order_id:
        print("❌ No order ID provided for status update test")
        return False
        
    print(f"\nTesting order status update with enum string for order {order_id}...")
    
    update_data = {
        "order_id": order_id,
        "status": "fulfilled"  # Using enum string
    }
    
    try:
        response = requests.put(f"{BASE_URL}/app/v1/update_order_status", json=update_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Order status updated successfully: {result}")
            return True
        else:
            print(f"❌ Order status update failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Order status update error: {e}")
        return False

def test_update_order_status_with_integer(order_id):
    """Test updating order status with integer code"""
    if not order_id:
        print("❌ No order ID provided for status update test")
        return False
        
    print(f"\nTesting order status update with integer code for order {order_id}...")
    
    update_data = {
        "order_id": order_id,
        "status": 14  # Using integer code for canceled
    }
    
    try:
        response = requests.put(f"{BASE_URL}/app/v1/update_order_status", json=update_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Order status updated successfully: {result}")
            return True
        else:
            print(f"❌ Order status update failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Order status update error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Starting enum functionality tests...\n")
    
    # Test health
    if not test_health():
        sys.exit(1)
    
    # Test order creation with enum status
    order_id_1 = test_create_order_with_enum_status()
    
    # Test order creation with integer status
    order_id_2 = test_create_order_with_integer_status()
    
    # Test status updates
    if order_id_1:
        test_update_order_status_with_enum(order_id_1)
    
    if order_id_2:
        test_update_order_status_with_integer(order_id_2)
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    main()
